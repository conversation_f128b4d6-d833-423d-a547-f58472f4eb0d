/**
 * 坐标转换功能测试文件
 * 用于验证 useMapData 中的坐标转换功能
 */

import { wgs84ToGcj02, gcj02ToWgs84, type CoordPoint } from '@/utils/coordTransformNew'

/**
 * 测试坐标转换功能
 */
export function testCoordinateTransform() {
  console.log('🧪 开始坐标转换功能测试...')

  // 测试用的坐标点（长春市附近）
  const testPoints = [
    { name: '长春市中心', latitude: 43.8171, longitude: 125.3235 },
    { name: '长春站', latitude: 43.8038, longitude: 125.3119 },
    { name: '吉林大学', latitude: 43.8642, longitude: 125.3347 },
  ]

  testPoints.forEach((point, index) => {
    console.log(`\n📍 测试点 ${index + 1}: ${point.name}`)
    console.log('原始坐标 (WGS84):', point)

    // WGS84 → GCJ02
    const gcjPoint = wgs84ToGcj02(point)
    console.log('转换后坐标 (GCJ02):', gcjPoint)

    // 计算偏移量
    const latOffset = ((gcjPoint.latitude - point.latitude) * 111000).toFixed(2)
    const lngOffset = ((gcjPoint.longitude - point.longitude) * 111000 * Math.cos((point.latitude * Math.PI) / 180)).toFixed(2)
    
    console.log('偏移量:', {
      纬度: latOffset + '米',
      经度: lngOffset + '米',
      总偏移: Math.sqrt(Number(latOffset) ** 2 + Number(lngOffset) ** 2).toFixed(2) + '米'
    })

    // GCJ02 → WGS84 (验证逆转换)
    const wgsBack = gcj02ToWgs84(gcjPoint)
    console.log('逆转换坐标 (WGS84):', wgsBack)

    // 计算精度损失
    const precisionLoss = Math.sqrt(
      (wgsBack.latitude - point.latitude) ** 2 + 
      (wgsBack.longitude - point.longitude) ** 2
    ) * 111000

    console.log('往返精度损失:', precisionLoss.toFixed(2) + '米')
  })

  console.log('\n✅ 坐标转换功能测试完成')
}

/**
 * 模拟 getValueByKey 返回的坐标字符串解析和转换
 */
export function testMapCenterTransform(coordString: string = '125.3235, 43.8171') {
  console.log('\n🗺️ 测试地图中心坐标转换...')
  console.log('输入坐标字符串:', coordString)

  try {
    // 解析坐标字符串
    const coords = coordString.split(',').map((coord) => parseFloat(coord.trim()))
    
    if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
      const [lng, lat] = coords
      
      console.log('解析后的坐标 (WGS84):', { latitude: lat, longitude: lng })
      
      // 转换为 GCJ02
      const wgsPoint: CoordPoint = { latitude: lat, longitude: lng }
      const gcjPoint = wgs84ToGcj02(wgsPoint)
      
      console.log('转换后的坐标 (GCJ02):', gcjPoint)
      
      // 计算偏移量
      const latOffset = ((gcjPoint.latitude - lat) * 111000).toFixed(2)
      const lngOffset = ((gcjPoint.longitude - lng) * 111000 * Math.cos((lat * Math.PI) / 180)).toFixed(2)
      
      console.log('偏移量:', {
        纬度: latOffset + '米',
        经度: lngOffset + '米'
      })
      
      return {
        original: wgsPoint,
        transformed: gcjPoint,
        offset: {
          latitude: Number(latOffset),
          longitude: Number(lngOffset)
        }
      }
    } else {
      throw new Error('坐标格式无效')
    }
  } catch (error) {
    console.error('❌ 坐标转换测试失败:', error)
    return null
  }
}

/**
 * 在浏览器控制台中运行测试
 * 可以在页面加载后调用此函数进行测试
 */
export function runCoordTransformTests() {
  console.log('🚀 开始运行坐标转换测试套件...')
  
  // 基础转换测试
  testCoordinateTransform()
  
  // 地图中心坐标转换测试
  testMapCenterTransform()
  testMapCenterTransform('129.5040, 42.9156') // 另一个测试坐标
  
  console.log('\n🎉 所有测试完成！')
}
