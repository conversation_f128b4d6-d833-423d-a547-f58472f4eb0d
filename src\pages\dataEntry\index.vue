<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '数据录入',
  },
}
</route>

<template>
  <view class="page-container">
    <!-- 地图视图 -->
    <view v-if="current === '地图'" class="map-view">
      <view class="map-container">
        <map
          id="myMap"
          :enable-satellite="true"
          :latitude="mapData.latitude.value"
          :longitude="mapData.longitude.value"
          :markers="mapData.covers.value"
          :polygons="mapData.allPolygons.value"
          :polyline="mapData.allPolylines.value"
          :scale="mapData.scale.value"
          :show-location="false"
          class="map-container"
          @regionchange="mapData.handleRegionChange"
        >
          <!-- 中心标记 -->
          <CenterMarker
            v-if="mapData.isShowCenterMarker.value"
            v-model:marker="mapData.centerMarker.value"
            :is-moving="mapData.isMapMoving.value"
          />

          <!-- 绘制视图 -->
          <DrawView
            v-if="isDrawing && drawingType > 0"
            v-model:marker="mapData.centerMarker.value"
            :is-moving="mapData.isMapMoving.value"
            @cancel="handleDrawCancel"
            @confirm="handleDrawConfirm"
            @delete="handleDrawDelete"
          />

          <!-- 地图缩放控制 -->
          <view class="map-zoom-controls">
            <view class="zoom-button zoom-in" @click="mapData.zoomIn">
              <text class="zoom-icon">+</text>
            </view>
            <view class="zoom-button zoom-out" @click="mapData.zoomOut">
              <text class="zoom-icon">−</text>
            </view>
            <!--            <view class="zoom-button zoom-btn" @click="handleLocationSelect">-->
            <!--              <image src="/static/map/icon/lock.png" style="width: 14px; height: 14px" />-->
            <!--            </view>-->
            <!--            <view class="zoom-button zoom-out zoom-button-blue" @click="switchToList">-->
            <!--              <wd-icon color="#ffffff" name="list" size="14px" />-->
            <!--            </view>-->
          </view>

          <!-- 地图信息面板 -->
          <view class="entry-data">
            <view class="map-controls-section">
              <view class="control-buttons">
                <view>地图信息</view>
                <view class="search-btn" size="small" @click="showSearchModal">
                  <wd-icon name="search" size="14px" />
                  搜索地点
                </view>
                <view class="location-btn" size="small" @click="handleLocationSelect">
                  <wd-icon name="location" size="14px" />
                  {{ getLocationButtonText() }}
                </view>
                <view class="list-btn" size="small" @click="switchToList">
                  <wd-icon name="list" size="14px" />
                  列表
                </view>
              </view>
            </view>

            <HouseInfoPanel :house="selectedHouse" @submit="handleStartFillReport" />
          </view>

          <!-- 地点搜索弹窗 -->
          <wd-popup
            v-model="searchModalVisible"
            position="bottom"
            :safe-area-inset-bottom="true"
            custom-style="height: 70vh; border-radius: 20px 20px 0 0;"
          >
            <view class="search-modal">
              <view class="search-header">
                <view class="search-title">搜索地点</view>
                <view class="search-close" @click="closeSearchModal">
                  <wd-icon name="close" size="20px" />
                </view>
              </view>

              <view class="search-input-section">
                <wd-search
                  v-model="searchKeyword"
                  placeholder="请输入地点名称"
                  :show-action="false"
                  @search="handleSearch"
                  @input="handleSearchInput"
                />
              </view>

              <view class="search-results">
                <view v-if="searchLoading" class="search-loading">
                  <wd-loading type="spinner" />
                  <text>搜索中...</text>
                </view>

                <view v-else-if="searchResults.length > 0" class="results-list">
                  <view
                    v-for="(item, index) in searchResults"
                    :key="index"
                    class="result-item"
                    @click="selectSearchResult(item)"
                  >
                    <view class="result-main">
                      <view class="result-name">{{ item.name }}</view>
                      <view class="result-address">{{ item.address }}</view>
                    </view>
                    <view class="result-distance" v-if="item.distance">
                      {{ formatDistance(item.distance) }}
                    </view>
                  </view>
                </view>

                <view v-else-if="searchKeyword && !searchLoading" class="no-results">
                  <wd-icon name="search" size="48px" color="#ccc" />
                  <text>未找到相关地点</text>
                </view>

                <view v-else class="search-tips">
                  <wd-icon name="location" size="48px" color="#ccc" />
                  <text>请输入地点名称进行搜索</text>
                </view>
              </view>
            </view>
          </wd-popup>
        </map>
      </view>
    </view>

    <!-- 列表视图 -->
    <view v-if="current === '列表'" class="list-view">
      <HouseListView
        :house-data="taskData.originalDataList.value"
        :is-loading="isLoading"
        :task-id="taskId"
        :task-type="taskType"
        @start-fill-report="handleStartFillReport"
        @view-on-map="handleViewOnMap"
        @switch-to-map="switchToMap"
        @data-loaded="handleDataLoaded"
        @refresh-data="refreshData"
      />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { onHide, onLoad, onShow } from '@dcloudio/uni-app'
import CenterMarker from '@/components/lomoMap/CenterMarker.vue'
import DrawView from '@/components/lomoMap/DrawView.vue'
import HouseInfoPanel from './components/HouseInfoPanel.vue'
import HouseListView from './components/HouseListView.vue'
import usePolygonDraw from '@/hooks/usePolygonDraw'
import { useMapData } from './composables/useMapData'
import { useTaskData } from './composables/useTaskData'
import { withErrorHandling } from './utils/helpers'

// ==================== 页面状态管理 ====================
const current = ref('列表')
const isLoading = ref(false)
const selectedHouse = ref(null)

// 任务信息
const taskId = ref<string | null>(null)
const taskTitle = ref('')
const taskType = ref<string>('ZF') // 默认为ZF

// 页面状态管理
const isFirstLoad = ref(true) // 标记是否首次加载
const lastNavigateTime = ref(0) // 记录最后一次跳转时间

// 动态文本配置
const getDataTypeText = () => {
  return taskType.value === 'ZF' ? '房屋' : '区域'
}

const getLocationButtonText = () => {
  return taskType.value === 'ZF' ? '选择地点' : '选择区域'
}

const getLocationToastText = (count: number) => {
  const dataType = getDataTypeText()
  return count > 0 ? `找到 ${count} 个${dataType}` : `该位置未找到${dataType}`
}

// ==================== Composables ====================
const mapData = useMapData()
const taskData = useTaskData()

// 绘制功能
const { isDrawing, drawingType, addPoint, removeLastPoint, clearPolygons, currentPolygon } =
  usePolygonDraw()

// ==================== 页面初始化 ====================
onLoad((option) => {
  taskId.value = option.id
  // 解码标题以修复乱码问题
  taskTitle.value = option.title ? decodeURIComponent(option.title) : ''
  taskType.value = option.taskType || 'ZF' // 从路由参数获取taskType，默认为ZF

  console.log('📝 页面参数:', {
    taskId: taskId.value,
    taskTitle: taskTitle.value,
    taskType: taskType.value,
  })

  uni.setNavigationBarTitle({
    title: taskTitle.value,
  })
})

onShow(() => {
  console.log('📱 数据录入页面显示')

  // 智能刷新逻辑：
  // 1. 首次加载时必须初始化数据
  // 2. 从子页面返回时刷新数据（检测是否从填报页面返回）
  const currentTime = Date.now()
  const timeSinceLastNavigate = currentTime - lastNavigateTime.value
  const shouldRefresh =
    isFirstLoad.value || (timeSinceLastNavigate > 500 && timeSinceLastNavigate < 300000) // 0.5秒到5分钟内返回认为是从子页面返回

  if (shouldRefresh) {
    console.log('🔄 检测到需要刷新数据:', {
      isFirstLoad: isFirstLoad.value,
      timeSinceLastNavigate,
      reason: isFirstLoad.value ? '首次加载' : '从子页面返回',
    })

    // 如果是从子页面返回，显示刷新提示
    if (!isFirstLoad.value) {
      uni.showToast({
        title: '正在刷新数据...',
        icon: 'loading',
        duration: 1000,
      })
    }

    initPageData()
    isFirstLoad.value = false
  } else {
    console.log('📱 页面显示但无需刷新数据:', {
      timeSinceLastNavigate,
      reason: timeSinceLastNavigate <= 500 ? '刚刚跳转' : '超时无需刷新',
    })
  }
})

onHide(() => {
  console.log('📱 数据录入页面隐藏')
})

onMounted(() => {
  console.log('📱 数据录入页面已挂载')
})

onUnmounted(() => {
  console.log('📱 数据录入页面已卸载')
})

// ==================== 数据初始化 ====================
/**
 * 初始化页面数据
 */
const initPageData = withErrorHandling(async () => {
  if (!taskId.value) {
    throw new Error('任务ID不存在')
  }

  isLoading.value = true

  try {
    // 首先获取地图初始坐标
    await mapData.getInitialMapCenter()

    // 并行加载数据，传递taskType参数
    const [data] = await Promise.all([taskData.initTaskData(taskId.value, taskType.value)])

    // 处理地图数据
    mapData.clearMapData()
    mapData.processGlobalWktData()
    mapData.processHouseData(data, taskType.value)

    console.log('✅ 页面数据初始化完成:', {
      taskId: taskId.value,
      taskType: taskType.value,
      dataCount: data.length,
      dataType: getDataTypeText(),
    })
  } catch (error) {
    console.error('❌ 页面数据初始化失败:', error)
    throw error
  } finally {
    isLoading.value = false
  }
}, '页面数据加载失败')

// ==================== 事件处理器 ====================
/**
 * 处理位置选择
 */
const handleLocationSelect = withErrorHandling(async () => {
  if (!taskId.value) {
    uni.showToast({ title: '任务ID不存在', icon: 'error' })
    return
  }

  // 清除之前选中的房屋
  selectedHouse.value = null
  mapData.clearLocationHouseData()

  // 验证中心点坐标
  const centerPoint = mapData.centerMarker.value
  if (!centerPoint?.latitude || !centerPoint?.longitude) {
    uni.showToast({ title: '地图坐标无效', icon: 'error' })
    return
  }

  uni.showLoading({ title: '查询中...' })

  try {
    const houses = await taskData.fetchHouseByLocation({
      taskId: parseInt(taskId.value),
      latitude: centerPoint.latitude,
      longitude: centerPoint.longitude,
    })

    // 🐛 添加调试信息
    console.log('🔍 位置查询返回的房屋数据:', {
      查询到的房屋数量: houses.length,
      房屋数据详情: houses.map((house) => ({
        id: house.fwjzdm,
        status: house.status,
        status类型: typeof house.status,
        完整数据: house,
      })),
    })

    if (houses.length > 0) {
      // 🚀 从原始数据列表中查找对应的房屋，获取完整状态信息
      const originalHouse = taskData.originalDataList.value.find(
        (original) => original.id?.toString() === houses[0].id?.toString(),
      )

      // 如果找到原始数据，合并状态信息
      if (originalHouse) {
        selectedHouse.value = {
          ...houses[0],
          status: originalHouse.status, // 使用原始数据的状态
          completionRate: originalHouse.completionRate || 0, // 添加完成率
        }
        console.log('✅ 找到原始房屋数据，状态同步:', {
          原始状态: originalHouse.status,
          合并后状态: selectedHouse.value.status,
        })
      } else {
        selectedHouse.value = houses[0]
        console.log('⚠️ 未找到原始房屋数据，使用位置查询结果')
      }

      // 在地图上显示查询结果
      mapData.processLocationHouseData(houses, taskType.value)
      uni.showToast({
        title: getLocationToastText(houses.length),
        icon: 'success',
      })
    } else {
      uni.showToast({
        title: getLocationToastText(0),
        icon: 'none',
      })
    }
  } finally {
    uni.hideLoading()
  }
}, '位置查询失败')

/**
 * 处理开始填报
 */
const handleStartFillReport = (house: any) => {
  if (!house?.id || !taskId.value) {
    uni.showToast({ title: '参数错误', icon: 'error' })
    return
  }

  // 记录跳转时间，用于返回时判断是否需要刷新数据
  lastNavigateTime.value = Date.now()

  // 构建调查对象标题
  let objectTitle = ''
  if (taskType.value === 'ZF') {
    // ZF任务显示：住房 + ID
    const id = house.fwjzdm ? house.fwjzdm.toString() : ''
    objectTitle = `${id}`
  } else {
    // 非ZF任务显示：name字段，如果没有name则显示区域 + ID
    const name = house.name || house.regionName
    if (name) {
      objectTitle = name
    } else {
      const id = house.id ? house.id.toString() : ''
      objectTitle = `区域 ${id}`
    }
  }

  const data = `?id=${house.id}&taskId=${taskId.value}&objectTitle=${encodeURIComponent(objectTitle)}&taskType=${taskType.value}`
  uni.navigateTo({
    url: `/pages/topicList/index${data}`,
    success: () => {
      console.log('🚀 成功跳转到填报页面，记录跳转时间:', lastNavigateTime.value)
    },
  })
}

/**
 * 处理在地图上查看
 */
const handleViewOnMap = (house: any) => {
  // 🚀 确保使用原始房屋数据，保持状态信息一致
  selectedHouse.value = house
  current.value = '地图'

  console.log('🗺️ 在地图上查看房屋:', {
    房屋ID: house.id,
    状态: house.status,
    状态类型: typeof house.status,
  })

  // 如果房屋有几何数据，在地图上显示
  if (house.geom) {
    mapData.clearLocationHouseData()
    mapData.processLocationHouseData([house], taskType.value)

    // 调整地图视野到房屋位置
    mapData.adjustMapViewToHouse(house)
  }

  uni.showToast({
    title: `已定位到${getDataTypeText()}`,
    icon: 'success',
  })
}

/**
 * 处理数据加载完成
 */
const handleDataLoaded = () => {
  isLoading.value = false
}

/**
 * 刷新数据
 */
const refreshData = async () => {
  if (!taskId.value) return

  try {
    isLoading.value = true
    const data = await taskData.refreshTaskData(taskId.value, taskType.value)

    // 处理地图数据
    mapData.clearMapData()
    mapData.processGlobalWktData()
    mapData.processHouseData(data, taskType.value)

    console.log('✅ 数据刷新完成:', {
      taskId: taskId.value,
      taskType: taskType.value,
      dataCount: data.length,
      dataType: getDataTypeText(),
    })
  } catch (error) {
    console.error('❌ 数据刷新失败:', error)
  } finally {
    isLoading.value = false
  }
}

/**
 * 视图切换
 */
const switchToMap = () => {
  current.value = '地图'
}

const switchToList = () => {
  current.value = '列表'
}

// ==================== 绘制功能事件处理 ====================
const handleDrawConfirm = (marker: any) => {
  addPoint({
    latitude: marker.latitude,
    longitude: marker.longitude,
  })

  const pointCount = currentPolygon.points.length
  let message = ''

  switch (drawingType.value) {
    case 1:
      message = '点标记已添加'
      break
    case 2:
      message = `线段点已添加 (${pointCount})`
      break
    case 3:
      message = `多边形点已添加 (${pointCount})`
      break
  }

  uni.showToast({ title: message, icon: 'success' })
}

const handleDrawCancel = () => {
  const success = removeLastPoint()

  if (success) {
    const message = drawingType.value === 1 ? '已删除上一个点标记' : '已删除上一个顶点'
    uni.showToast({ title: message, icon: 'success' })
  } else {
    uni.showToast({ title: '没有可删除的点', icon: 'none' })
  }
}

const handleDrawDelete = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空当前绘制内容吗？',
    success: (res) => {
      if (res.confirm) {
        clearPolygons()
        uni.showToast({ title: '绘制内容已清空', icon: 'success' })
      }
    },
  })
}
</script>

<style lang="scss" scoped>
// 使用CSS变量和现代布局
:root {
  --primary-color: #2563eb;
  --success-color: #16a34a;
  --warning-color: #ea580c;
  --danger-color: #dc2626;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --border-color: #e2e8f0;
  --shadow-md: 0 4px 6px -1px #0000001a, 0 2px 4px -1px #0000000f;
  --shadow-lg: 0 10px 15px -3px #0000001a, 0 4px 6px -2px #0000000d;
  --radius-lg: 8px;
  --radius-xl: 10px;
}

.page-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: var(--bg-secondary);
}

.map-view,
.list-view {
  width: 100%;
  height: 100%;
}

.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

// 地图缩放控制
.map-zoom-controls {
  position: absolute;
  top: 80px;
  right: 20px;
  z-index: 150;
  display: flex;
  flex-direction: column;
  gap: 2px;

  .zoom-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: #ffffffe6;
    border: 1px solid #0000001a;
    border-radius: 6px;
    box-shadow: var(--shadow-md);
    transition: all 0.2s ease;

    &:hover {
      background: #ffffff;
      box-shadow: var(--shadow-lg);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    .zoom-icon {
      font-size: 18px;
      font-weight: bold;
      line-height: 1;
      color: #333333;
    }
  }
  .zoom-button-blue {
    background: var(--primary-color);
  }
  .zoom-in .zoom-icon {
    color: var(--primary-color);
  }
  .zoom-in .zoom-out .zoom-icon {
    color: #666666;
  }
  .zoom-btn {
    margin-top: 10px;
  }
}

// 地图信息面板 - 紧凑化设计
.entry-data {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px 0 40px 0;
  background: var(--bg-primary);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  box-shadow: var(--shadow-lg);
  .map-controls-section {
    margin-bottom: 10px;

    .control-buttons {
      display: flex;
      gap: 10px;
      align-items: center;
      justify-content: space-around;

      .location-btn {
        display: flex;
        gap: 4px;
        align-items: center;
        height: 32px;
        padding: 0 12px;
        font-size: 12px;
        font-weight: 600;
        color: white;
        background: var(--primary-color);
        border: none;
        border-radius: 6px;
        box-shadow: var(--shadow-md);
        transition: all 0.2s ease;

        &:hover {
          box-shadow: var(--shadow-lg);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .list-btn {
        display: flex;
        gap: 4px;
        align-items: center;
        height: 32px;
        padding: 0 12px;
        font-size: 12px;
        font-weight: 600;
        color: white;
        background: var(--success-color);
        border: none;
        border-radius: 6px;
        box-shadow: var(--shadow-md);
        transition: all 0.2s ease;

        &:hover {
          box-shadow: var(--shadow-lg);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --border-color: #334155;
  }
}
</style>
