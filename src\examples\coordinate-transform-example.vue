<!--
  坐标转换功能使用示例
  展示如何在页面中使用 useMapData 的坐标转换功能
-->
<template>
  <view class="coord-transform-example">
    <view class="header">
      <text class="title">坐标转换功能示例</text>
    </view>

    <!-- 配置面板 -->
    <view class="config-panel">
      <view class="config-item">
        <text class="label">启用坐标转换:</text>
        <switch 
          :checked="mapData.coordTransformConfig.enableCoordTransform" 
          @change="onTransformToggle"
        />
      </view>
      
      <view class="config-item">
        <text class="label">源坐标系:</text>
        <picker 
          :value="sourceCoordIndex" 
          :range="coordSystems" 
          @change="onSourceCoordChange"
        >
          <view class="picker-text">{{ mapData.coordTransformConfig.sourceCoordSystem }}</view>
        </picker>
      </view>
      
      <view class="config-item">
        <text class="label">目标坐标系:</text>
        <picker 
          :value="targetCoordIndex" 
          :range="coordSystems" 
          @change="onTargetCoordChange"
        >
          <view class="picker-text">{{ mapData.coordTransformConfig.targetCoordSystem }}</view>
        </picker>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-panel">
      <button @click="getMapCenter" :loading="loading">获取地图中心坐标</button>
      <button @click="runTests">运行坐标转换测试</button>
    </view>

    <!-- 结果显示 -->
    <view class="result-panel" v-if="result">
      <view class="result-title">转换结果:</view>
      <view class="result-item">
        <text class="result-label">纬度:</text>
        <text class="result-value">{{ result.latitude.toFixed(6) }}</text>
      </view>
      <view class="result-item">
        <text class="result-label">经度:</text>
        <text class="result-value">{{ result.longitude.toFixed(6) }}</text>
      </view>
    </view>

    <!-- 地图显示 -->
    <view class="map-container" v-if="result">
      <map
        :latitude="result.latitude"
        :longitude="result.longitude"
        :scale="15"
        :show-location="true"
        class="map"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMapData } from '@/pages/dataEntry/composables/useMapData'
import { runCoordTransformTests } from '@/utils/coordTransformTest'

// 使用地图数据 composable
const mapData = useMapData()

// 响应式数据
const loading = ref(false)
const result = ref<{ latitude: number; longitude: number } | null>(null)

// 坐标系选项
const coordSystems = ['WGS84', 'GCJ02']

// 计算当前选中的坐标系索引
const sourceCoordIndex = computed(() => {
  return coordSystems.indexOf(mapData.coordTransformConfig.sourceCoordSystem)
})

const targetCoordIndex = computed(() => {
  return coordSystems.indexOf(mapData.coordTransformConfig.targetCoordSystem)
})

// 事件处理函数
const onTransformToggle = (e: any) => {
  mapData.coordTransformConfig.enableCoordTransform = e.detail.value
  console.log('坐标转换开关:', e.detail.value)
}

const onSourceCoordChange = (e: any) => {
  const index = e.detail.value
  mapData.coordTransformConfig.sourceCoordSystem = coordSystems[index] as 'WGS84' | 'GCJ02'
  console.log('源坐标系已更改为:', coordSystems[index])
}

const onTargetCoordChange = (e: any) => {
  const index = e.detail.value
  mapData.coordTransformConfig.targetCoordSystem = coordSystems[index] as 'WGS84' | 'GCJ02'
  console.log('目标坐标系已更改为:', coordSystems[index])
}

// 获取地图中心坐标
const getMapCenter = async () => {
  try {
    loading.value = true
    console.log('🗺️ 开始获取地图中心坐标...')
    
    const center = await mapData.getInitialMapCenter()
    result.value = center
    
    uni.showToast({
      title: '坐标获取成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('❌ 获取地图中心坐标失败:', error)
    uni.showToast({
      title: '获取坐标失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 运行测试
const runTests = () => {
  try {
    runCoordTransformTests()
    uni.showToast({
      title: '测试完成，请查看控制台',
      icon: 'success'
    })
  } catch (error) {
    console.error('❌ 测试运行失败:', error)
    uni.showToast({
      title: '测试失败',
      icon: 'error'
    })
  }
}

// 页面加载时自动获取坐标
onMounted(() => {
  getMapCenter()
})
</script>

<style scoped>
.coord-transform-example {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.config-panel {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 5rpx;
}

.action-panel {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-panel button {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.result-panel {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.result-label {
  font-size: 28rpx;
  color: #666;
}

.result-value {
  font-size: 28rpx;
  color: #333;
  font-family: monospace;
}

.map-container {
  height: 400rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.map {
  width: 100%;
  height: 100%;
}
</style>
