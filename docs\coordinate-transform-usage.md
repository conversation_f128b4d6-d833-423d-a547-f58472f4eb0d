# 地图初始坐标转换功能使用指南

## 概述

在 `useMapData` composable 中新增了自动坐标转换功能，用于处理从服务器获取的地图初始坐标。该功能可以自动将不同坐标系的坐标转换为地图显示所需的坐标系。

## 功能特性

- ✅ 支持 WGS84 ↔ GCJ02 坐标系转换
- ✅ 可配置的坐标转换开关
- ✅ 详细的转换日志和偏移量计算
- ✅ 错误处理和降级机制
- ✅ 精度损失评估

## 配置说明

### 坐标转换配置

```typescript
const coordTransformConfig = {
  enableCoordTransform: true,        // 是否启用坐标转换
  sourceCoordSystem: 'WGS84',        // 源坐标系类型
  targetCoordSystem: 'GCJ02',        // 目标坐标系类型
}
```

### 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enableCoordTransform` | boolean | `true` | 是否启用坐标转换功能 |
| `sourceCoordSystem` | `'WGS84' \| 'GCJ02'` | `'WGS84'` | 服务器返回的坐标系类型 |
| `targetCoordSystem` | `'WGS84' \| 'GCJ02'` | `'GCJ02'` | 地图显示使用的坐标系类型 |

## 使用方法

### 基本使用

```typescript
import { useMapData } from '@/pages/dataEntry/composables/useMapData'

const mapData = useMapData()

// 获取地图初始坐标（自动进行坐标转换）
const center = await mapData.getInitialMapCenter()
console.log('地图中心坐标:', center)
```

### 修改坐标转换配置

```typescript
const mapData = useMapData()

// 禁用坐标转换
mapData.coordTransformConfig.enableCoordTransform = false

// 修改源坐标系类型
mapData.coordTransformConfig.sourceCoordSystem = 'GCJ02'

// 修改目标坐标系类型
mapData.coordTransformConfig.targetCoordSystem = 'WGS84'
```

### 在页面中使用

```vue
<script setup>
import { onMounted } from 'vue'
import { useMapData } from '@/pages/dataEntry/composables/useMapData'

const mapData = useMapData()

onMounted(async () => {
  // 根据项目需求配置坐标转换
  if (项目使用高德地图) {
    mapData.coordTransformConfig.targetCoordSystem = 'GCJ02'
  } else if (项目使用原生GPS) {
    mapData.coordTransformConfig.targetCoordSystem = 'WGS84'
  }
  
  // 获取并设置地图初始坐标
  await mapData.getInitialMapCenter()
})
</script>
```

## 坐标系说明

### WGS84 坐标系
- **全称**: World Geodetic System 1984
- **用途**: GPS 设备、国际标准
- **特点**: 全球统一的地理坐标系

### GCJ02 坐标系
- **全称**: 国家测绘局坐标系
- **别名**: 火星坐标系
- **用途**: 中国地图服务（高德、腾讯等）
- **特点**: 在 WGS84 基础上加密偏移

## 转换精度

- **典型偏移量**: 300-800米（根据地理位置不同）
- **往返精度损失**: < 1米
- **转换库**: coordtransform

## 日志输出示例

```
🗺️ 开始获取地图初始坐标...
✅ 成功获取地图初始坐标 (WGS84): { latitude: 43.8171, longitude: 125.3235 }
🔄 坐标转换完成 (WGS84 → GCJ02): {
  原始坐标: { latitude: 43.8171, longitude: 125.3235 },
  转换后坐标: { latitude: 43.8201, longitude: 125.3298 },
  偏移量: {
    纬度: 333.00米,
    经度: 445.20米
  }
}
```

## 测试功能

项目提供了测试工具来验证坐标转换功能：

```typescript
import { runCoordTransformTests } from '@/utils/coordTransformTest'

// 在浏览器控制台运行测试
runCoordTransformTests()
```

## 常见问题

### Q: 如何确定服务器返回的坐标系类型？

A: 可以通过以下方式判断：
1. 查看服务器 API 文档
2. 对比实际坐标与地图显示位置
3. 咨询后端开发人员

### Q: 转换后的坐标不准确怎么办？

A: 检查以下几点：
1. 确认源坐标系配置是否正确
2. 检查坐标字符串格式是否正确
3. 验证坐标是否在中国境内

### Q: 如何禁用坐标转换？

A: 设置 `enableCoordTransform` 为 `false`：

```typescript
mapData.coordTransformConfig.enableCoordTransform = false
```

## 更新日志

### v1.0.0 (2025-01-17)
- ✅ 新增自动坐标转换功能
- ✅ 支持 WGS84 ↔ GCJ02 转换
- ✅ 可配置的转换参数
- ✅ 详细的转换日志
- ✅ 测试工具和文档
